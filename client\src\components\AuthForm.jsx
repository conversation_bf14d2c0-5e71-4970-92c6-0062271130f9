import { cn } from "@/lib/utils";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useLocation, Link } from "react-router-dom";
import { useRegister } from "@/hooks/useRegister";

import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useMemo, useState } from "react";
import { toast } from "sonner";

// ------------------ ZOD SCHEMAS ------------------ //
const registerSchema = z.object({
  username: z.string().min(3, "Username is too short"),
  email: z.string().email("Invalid email address"),
  password: z.string().min(6, "Password must be at least 6 characters"),
});

const loginSchema = z.object({
  email: z.string().email("Invalid email address"),
  password: z.string().min(1, "Password is required"),
});

const forgotSchema = z.object({
  email: z.string().email("Invalid email address"),
});

const resetSchema = z
  .object({
    password: z.string().min(6, "Password must be at least 6 characters"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "Passwords do not match",
    path: ["confirmPassword"],
  });

// ------------------ MAIN COMPONENT ------------------ //
export function AuthForm({ className, ...props }) {
  const location = useLocation();
  const path = location.pathname;

  const { handleRegister, loading, error, success } = useRegister();

  const schema = useMemo(() => {
    switch (path) {
      case "/register":
        return registerSchema;
      case "/login":
        return loginSchema;
      case "/forgot-password":
        return forgotSchema;
      default:
        return path.startsWith("/reset-password")
          ? resetSchema
          : registerSchema;
    }
  }, [path]);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm({
    resolver: zodResolver(schema),
  });

  const [form, setForm] = useState({
    username: "",
    email: "",
    password: "",
    confirmPassword: "",
  });

  const onSubmit = async (data) => {
    if (path === "/register") {
      try {
        await handleRegister(data);
        toast.success(
          "Registration successful! Check your email for verification."
        );
        setForm({
          username: "",
          email: "",
          password: "",
          confirmPassword: "",
        });
      } catch (err) {
        toast.error(err.message || "Something went wrong");
      }
    }

    // Add logic for login, forgot-password, etc. if needed
  };

  const renderFields = () => {
    switch (path) {
      case "/register":
        return (
          <>
            <div className="grid gap-3">
              <Label htmlFor="username">Username</Label>
              <Input
                id="username"
                {...register("username")}
                placeholder="Enter your username"
              />
              {errors.username && (
                <p className="text-red-500 text-sm">
                  {errors.username.message}
                </p>
              )}
            </div>
            <div className="grid gap-3">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                {...register("email")}
                placeholder="<EMAIL>"
              />
              {errors.email && (
                <p className="text-red-500 text-sm">{errors.email.message}</p>
              )}
            </div>
            <div className="grid gap-3">
              <Label htmlFor="password">Password</Label>
              <Input
                id="password"
                type="password"
                {...register("password")}
                placeholder="*********"
              />
              {errors.password && (
                <p className="text-red-500 text-sm">
                  {errors.password.message}
                </p>
              )}
            </div>
          </>
        );

      case "/login":
        return (
          <>
            <div className="grid gap-3">
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" {...register("email")} />
              {errors.email && (
                <p className="text-red-500 text-sm">{errors.email.message}</p>
              )}
            </div>
            <div className="grid gap-3">
              <div className="flex items-center">
                <Label htmlFor="password">Password</Label>
                <Link
                  to="/forgot-password"
                  className="ml-auto text-sm underline-offset-4 hover:underline"
                >
                  Forgot your password?
                </Link>
              </div>
              <Input id="password" type="password" {...register("password")} />
              {errors.password && (
                <p className="text-red-500 text-sm">
                  {errors.password.message}
                </p>
              )}
            </div>
          </>
        );

      case "/forgot-password":
        return (
          <div className="grid gap-3">
            <Label htmlFor="email">Email</Label>
            <Input id="email" type="email" {...register("email")} />
            {errors.email && (
              <p className="text-red-500 text-sm">{errors.email.message}</p>
            )}
          </div>
        );

      default:
        if (path.startsWith("/reset-password")) {
          return (
            <>
              <div className="grid gap-3">
                <Label htmlFor="password">New Password</Label>
                <Input
                  id="password"
                  type="password"
                  {...register("password")}
                />
                {errors.password && (
                  <p className="text-red-500 text-sm">
                    {errors.password.message}
                  </p>
                )}
              </div>
              <div className="grid gap-3">
                <Label htmlFor="confirmPassword">Confirm Password</Label>
                <Input
                  id="confirmPassword"
                  type="password"
                  {...register("confirmPassword")}
                />
                {errors.confirmPassword && (
                  <p className="text-red-500 text-sm">
                    {errors.confirmPassword.message}
                  </p>
                )}
              </div>
            </>
          );
        }
    }
  };

  const titleMap = {
    "/register": "Create an account",
    "/login": "Login to your account",
    "/forgot-password": "Forgot your password?",
  };

  return (
    <form
      onSubmit={handleSubmit(onSubmit)}
      className={cn("flex flex-col gap-6", className)}
      {...props}
    >
      <div className="flex flex-col items-center gap-2 text-center">
        <h1 className="text-2xl font-bold">
          {titleMap[path] || "Reset your password"}
        </h1>
        <p className="text-muted-foreground text-sm text-balance">
          {path === "/register"
            ? "Enter your details to register."
            : path === "/forgot-password"
            ? "We'll send a reset link to your email."
            : "Enter your credentials to continue."}
        </p>
      </div>

      <div className="grid gap-6">
        {renderFields()}

        <Button type="submit" className="w-full" disabled={loading}>
          {loading
            ? "Submitting..."
            : path === "/register"
            ? "Register"
            : path === "/forgot-password"
            ? "Send Reset Link"
            : path.startsWith("/reset-password")
            ? "Reset Password"
            : "Login"}
        </Button>
      </div>

      {path === "/login" && (
        <div className="text-center text-sm">
          Don&apos;t have an account?{" "}
          <Link to="/register" className="underline">
            Sign up
          </Link>
        </div>
      )}
      {path === "/register" && (
        <div className="text-center text-sm">
          Already have an account?{" "}
          <Link to="/login" className="underline">
            Login
          </Link>
        </div>
      )}
      {path === "/forgot-password" && (
        <div className="text-center text-sm">
          Remember your password?{" "}
          <Link to="/login" className="underline">
            Login
          </Link>
        </div>
      )}
    </form>
  );
}
