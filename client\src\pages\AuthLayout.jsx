import { useLocation } from "react-router-dom";
import { GalleryVerticalEnd } from "lucide-react";

import { AuthForm } from "@/components/AuthForm";
import { ThemeToggle } from "@/components/ThemeToggle";

export default function AuthLayout() {
  const location = useLocation();

  // Extract mode from path
  const getModeFromPath = (path) => {
    if (path.includes("/register")) return "register";
    if (path.includes("/login")) return "login";
    if (path.includes("/forgot-password")) return "forgot";
    if (path.includes("/reset-password")) return "reset";
    return "login"; // fallback
  };

  const mode = getModeFromPath(location.pathname);

  return (
    <div className="grid min-h-svh lg:grid-cols-2">
      <div className="flex flex-col gap-4 p-6 md:p-10">
        <div className="flex justify-center gap-2 md:justify-start">
          <a href="#" className="flex items-center gap-2 font-medium">
            <div className="bg-primary text-primary-foreground flex size-6 items-center justify-center rounded-md">
              <GalleryVerticalEnd className="size-4" />
            </div>
            GigGlobe.
          </a>
        </div>
        <div className="flex flex-1 items-center justify-center">
          <div className="w-full max-w-xs">
            {/* ✅ Dynamic form here */}
            <AuthForm mode={mode} />
          </div>
        </div>
      </div>
      <div className="bg-muted relative hidden lg:block">
        <img
          src="https://images.pexels.com/photos/3184429/pexels-photo-3184429.jpeg"
          alt="Image"
          className="absolute inset-0 h-full w-full object-cover"
        />
        <div className="absolute bottom-5 right-5">
          <ThemeToggle />
        </div>
      </div>
    </div>
  );
}
